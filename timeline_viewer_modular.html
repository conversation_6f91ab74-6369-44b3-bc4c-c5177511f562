<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AWS CloudWatch Log Timeline Analyzer</title>
    <link rel="stylesheet" href="timeline_viewer.css">
</head>
<body>
    <div class="header">
        <h1>CloudWatch Log Timeline</h1>
        <p>Analyze job executions and performance with resizable columns</p>
    </div>

    <div class="upload-area" onclick="document.getElementById('fileInput').click()">
        <h3>📁 Upload JSON File</h3>
        <p>Drag & drop or click to browse</p>
        <input type="file" id="fileInput" accept=".json" style="display: none;">
    </div>

    <div id="stats" class="stats" style="display: none;">
        <div class="stat-box">
            <h3>Total Jobs</h3>
            <div id="totalJobs" class="stat-value">-</div>
        </div>
        <div class="stat-box">
            <h3>Unique Tenants</h3>
            <div id="uniqueTenants" class="stat-value">-</div>
        </div>
        <div class="stat-box">
            <h3>Longest Job</h3>
            <div id="longestJob" class="stat-value">-</div>
        </div>
        <div class="stat-box">
            <h3>Time Range</h3>
            <div id="timeRange" class="stat-value">-</div>
        </div>
        <div class="stat-box">
            <h3>Jobs/Hour Peak</h3>
            <div id="peakHour" class="stat-value">-</div>
        </div>
    </div>

    <div id="colorLegend" class="color-legend" style="display: none;">
        <strong>Color Legend:</strong>
        <div class="legend-item"><span class="legend-color" style="background: #28a745;"></span>Normal (&lt;5min)</div>
        <div class="legend-item"><span class="legend-color" style="background: #ffc107;"></span>Medium (5-30min)</div>
        <div class="legend-item"><span class="legend-color" style="background: #dc3545;"></span>Long (&gt;30min)</div>
        <div class="legend-item"><span class="legend-color" style="background: #007bff;"></span>ClearingHouse</div>
        <div class="legend-item"><span class="legend-color" style="background: #fd7e14;"></span>Clinical</div>
        <div class="legend-item"><span class="legend-color" style="background: #20c997;"></span>Quicksight</div>
    </div>

    <div id="chartContainer" class="chart-container" style="display: none;">
        <div class="chart-title">Jobs per Hour Timeline</div>
        <div class="timeline-chart" id="timelineChart"></div>
        <div class="chart-tooltip" id="chartTooltip"></div>
    </div>

    <div id="filterControls" class="filter-controls" style="display: none;">
        <label>Tenant: <input type="text" id="tenantFilter" placeholder="Filter by tenant"></label>
        <label>Job Type: 
            <select id="jobTypeFilter">
                <option value="">All Types</option>
                <option value="GeneralLedger">General Ledger</option>
                <option value="ClearingHouse">Clearing House</option>
                <option value="ClinicalOperations">Clinical Operations</option>
                <option value="QuicksightExport">Quicksight Export</option>
            </select>
        </label>
        <label>Min Duration (minutes): <input type="number" id="minDuration" placeholder="0" min="0" step="0.1"></label>
        <label>Log Stream: <input type="text" id="streamFilter" placeholder="Filter by log stream"></label>
        <button onclick="applyFilters()">Apply Filters</button>
        <button onclick="clearFilters()">Clear</button>
        <button onclick="exportFilteredData()">Export CSV</button>
    </div>

    <div id="timeline" class="timeline" style="display: none;">
        <div class="timeline-header">
            <div class="col-time" onclick="sortBy('time')">
                TIME
                <div class="resize-handle" onmousedown="startResize(event, 'time')"></div>
            </div>
            <div class="col-tenant" onclick="sortBy('tenant')">
                TENANT
                <div class="resize-handle" onmousedown="startResize(event, 'tenant')"></div>
            </div>
            <div class="col-jobtype" onclick="sortBy('jobType')">
                JOB TYPE
                <div class="resize-handle" onmousedown="startResize(event, 'jobtype')"></div>
            </div>
            <div class="col-duration" onclick="sortBy('duration')">
                DURATION
                <div class="resize-handle" onmousedown="startResize(event, 'duration')"></div>
            </div>
            <div class="col-operation" onclick="sortBy('operation')">
                OPERATION
                <div class="resize-handle" onmousedown="startResize(event, 'operation')"></div>
            </div>
            <div class="col-stream" onclick="sortBy('logStream')">
                LOG STREAM
                <div class="resize-handle" onmousedown="startResize(event, 'stream')"></div>
            </div>
        </div>
        <div class="timeline-content" id="timelineContent"></div>
    </div>

    <script src="timeline_viewer.js"></script>
</body>
</html>
