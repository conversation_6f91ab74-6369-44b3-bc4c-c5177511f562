<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AWS CloudWatch Log Timeline Analyzer</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            margin: 20px;
            background: #f5f5f5;
            color: #333;
            width: calc(100vw - 40px);
            max-width: none;
        }
        .header { 
            text-align: center; 
            margin-bottom: 15px; 
            padding: 10px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .header h1 { margin: 0 0 5px 0; font-size: 20px; }
        .header p { margin: 0; font-size: 14px; color: #666; }
        .upload-area { 
            border: 2px dashed #007acc; 
            padding: 15px; 
            text-align: center; 
            margin-bottom: 15px; 
            cursor: pointer;
            background: white;
            border-radius: 8px;
            transition: background 0.3s;
        }
        .upload-area h3 { margin: 0 0 5px 0; font-size: 16px; }
        .upload-area p { margin: 0; font-size: 12px; color: #666; }
        .upload-area:hover { background: #f0f8ff; }
        .timeline {
            max-height: 600px;
            overflow-y: auto;
            overflow-x: auto;
            border: 1px solid #ddd;
            background: white;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            font-size: 13px;
            width: 100%;
            min-width: 800px;
        }
        .timeline-header {
            background: #f8f9fa;
            padding: 10px 15px;
            border-bottom: 2px solid #ddd;
            font-weight: bold;
            position: sticky;
            top: 0;
            z-index: 10;
            display: grid;
            grid-template-columns: 150px 180px 140px 120px 400px 150px;
            gap: 10px;
            font-size: 12px;
            color: #666;
            width: 100%;
            min-width: 1140px;
            box-sizing: border-box;
        }
        .timeline-header div {
            cursor: pointer;
            user-select: none;
            position: relative;
            padding: 5px 20px 5px 5px;
            display: flex;
            align-items: center;
            justify-content: flex-start;
            min-width: 0;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
        .timeline-header div:hover {
            background: #e9ecef;
            border-radius: 3px;
        }
        .resize-handle {
            position: absolute;
            right: -2px;
            top: 0;
            bottom: 0;
            width: 6px;
            cursor: col-resize;
            background: transparent;
            border-right: 2px solid transparent;
            transition: all 0.2s;
            z-index: 20;
        }
        .resize-handle:hover {
            border-right-color: #007acc;
            background: rgba(0, 122, 204, 0.1);
        }
        .resizing {
            cursor: col-resize !important;
        }
        .resizing * {
            cursor: col-resize !important;
        }
        .timeline-header div.sorted::after {
            content: '';
            position: absolute;
            right: 5px;
            top: 50%;
            transform: translateY(-50%);
            width: 0;
            height: 0;
            border-left: 4px solid transparent;
            border-right: 4px solid transparent;
        }
        .timeline-header div.sorted.asc::after {
            border-bottom: 6px solid #666;
        }
        .timeline-header div.sorted.desc::after {
            border-top: 6px solid #666;
        }
        
        .chart-container {
            margin: 20px 0;
            background: white;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            display: none;
        }
        .chart-title {
            text-align: center;
            font-weight: bold;
            margin-bottom: 15px;
            color: #333;
        }
        .timeline-chart {
            height: 200px;
            position: relative;
            border: 1px solid #ddd;
            background: linear-gradient(to right, #f8f9fa 0%, #ffffff 100%);
            overflow: hidden;
        }
        .chart-bar {
            position: absolute;
            bottom: 0;
            background: #007acc;
            border-radius: 2px 2px 0 0;
            transition: all 0.3s ease;
            cursor: pointer;
        }
        .chart-bar:hover {
            background: #005a9e;
        }
        .chart-bar.high-load {
            background: #dc3545;
        }
        .chart-bar.medium-load {
            background: #ffc107;
        }
        .chart-axis {
            position: absolute;
            bottom: -25px;
            font-size: 10px;
            color: #666;
            transform: translateX(-50%);
        }
        .chart-tooltip {
            position: absolute;
            background: rgba(0, 0, 0, 0.8);
            color: white;
            padding: 5px 8px;
            border-radius: 4px;
            font-size: 11px;
            pointer-events: none;
            z-index: 1000;
            display: none;
        }
        .timeline-content {
            padding: 15px;
        }
        .job-entry {
            margin: 2px 0;
            padding: 8px 15px;
            border-left: 4px solid #ddd;
            background: #fafafa;
            border-radius: 3px;
            display: grid;
            grid-template-columns: 150px 180px 140px 120px 400px 150px;
            gap: 10px;
            align-items: center;
            font-size: 12px;
            width: 100%;
            min-width: 1140px;
            box-sizing: border-box;
        }
        .job-entry > div {
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            min-width: 0;
            padding: 2px 4px;
        }
        .normal { border-left-color: #28a745; }
        .long-duration { border-left-color: #dc3545; background: #fff5f5; }
        .medium-duration { border-left-color: #ffc107; background: #fffbf0; }
        .clearing { border-left-color: #007bff; background: #f0f8ff; }
        .clinical { border-left-color: #fd7e14; background: #fff8f0; }
        .quicksight { border-left-color: #20c997; background: #f0fff8; }
        
        .stats { 
            display: grid; 
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); 
            gap: 15px; 
            margin-bottom: 20px; 
        }
        .stat-box { 
            background: white;
            border: 1px solid #ddd; 
            padding: 20px; 
            text-align: center;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .stat-value { font-size: 24px; font-weight: bold; color: #007acc; }
        
        .filter-controls { 
            margin: 15px 0; 
            padding: 12px; 
            background: white;
            border: 1px solid #ddd;
            border-radius: 8px;
            display: flex;
            gap: 10px;
            flex-wrap: wrap;
            align-items: center;
            font-size: 12px;
        }
        .filter-controls input, .filter-controls select { 
            padding: 4px 8px; 
            border: 1px solid #ddd; 
            border-radius: 4px;
            font-size: 12px;
        }
        .filter-controls button {
            background: #007acc;
            color: white;
            border: none;
            padding: 4px 12px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 12px;
        }
        
        .color-legend {
            background: white;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 10px;
            margin: 10px 0;
            font-size: 11px;
        }
        .legend-item {
            display: inline-block;
            margin: 2px 8px;
        }
        .legend-color {
            display: inline-block;
            width: 12px;
            height: 12px;
            border-radius: 2px;
            margin-right: 4px;
            vertical-align: middle;
        }
        .filter-controls button:hover { background: #005a9e; }
        
        .timestamp { color: #666; font-weight: bold; }
        .duration { font-weight: bold; }
        .tenant { color: #007acc; font-weight: bold; }
        .operation { color: #555; }
    </style>
</head>
<body>
    <div class="header">
        <h1>CloudWatch Log Timeline</h1>
        <p>Analyze job executions and performance</p>
    </div>

    <div class="upload-area" onclick="document.getElementById('fileInput').click()">
        <h3>📁 Upload JSON File</h3>
        <p>Drag & drop or click to browse</p>
        <input type="file" id="fileInput" accept=".json" style="display: none;">
    </div>

    <div id="stats" class="stats" style="display: none;">
        <div class="stat-box">
            <h3>Total Jobs</h3>
            <div id="totalJobs" class="stat-value">-</div>
        </div>
        <div class="stat-box">
            <h3>Unique Tenants</h3>
            <div id="uniqueTenants" class="stat-value">-</div>
        </div>
        <div class="stat-box">
            <h3>Longest Job</h3>
            <div id="longestJob" class="stat-value">-</div>
        </div>
        <div class="stat-box">
            <h3>Time Range</h3>
            <div id="timeRange" class="stat-value">-</div>
        </div>
        <div class="stat-box">
            <h3>Jobs/Hour Peak</h3>
            <div id="peakHour" class="stat-value">-</div>
        </div>
    </div>

    <div id="colorLegend" class="color-legend" style="display: none;">
        <strong>Color Legend:</strong>
        <div class="legend-item"><span class="legend-color" style="background: #28a745;"></span>Normal (&lt;5min)</div>
        <div class="legend-item"><span class="legend-color" style="background: #ffc107;"></span>Medium (5-30min)</div>
        <div class="legend-item"><span class="legend-color" style="background: #dc3545;"></span>Long (&gt;30min)</div>
        <div class="legend-item"><span class="legend-color" style="background: #007bff;"></span>ClearingHouse</div>
        <div class="legend-item"><span class="legend-color" style="background: #fd7e14;"></span>Clinical</div>
        <div class="legend-item"><span class="legend-color" style="background: #20c997;"></span>Quicksight</div>
    </div>

    <div id="chartContainer" class="chart-container" style="display: none;">
        <div class="chart-title">Jobs per Hour Timeline</div>
        <div class="timeline-chart" id="timelineChart"></div>
        <div class="chart-tooltip" id="chartTooltip"></div>
    </div>

    <div id="filterControls" class="filter-controls" style="display: none;">
        <label>Tenant: <input type="text" id="tenantFilter" placeholder="Filter by tenant"></label>
        <label>Job Type:
            <select id="jobTypeFilter">
                <option value="">All Types</option>
                <option value="GeneralLedger">General Ledger</option>
                <option value="ClearingHouse">Clearing House</option>
                <option value="ClinicalOperations">Clinical Operations</option>
                <option value="QuicksightExport">Quicksight Export</option>
            </select>
        </label>
        <label>Min Duration (minutes): <input type="number" id="minDuration" placeholder="0" min="0" step="0.1"></label>
        <label>Log Stream: <input type="text" id="streamFilter" placeholder="Filter by log stream"></label>
        <button onclick="applyFilters()">Apply Filters</button>
        <button onclick="clearFilters()">Clear</button>
        <button onclick="exportFilteredData()">Export CSV</button>
    </div>

    <div id="timeline" class="timeline" style="display: none;">
        <div class="timeline-header">
            <div class="col-time" onclick="sortBy('time')">
                TIME
                <div class="resize-handle" onmousedown="startResize(event, 'time')"></div>
            </div>
            <div class="col-tenant" onclick="sortBy('tenant')">
                TENANT
                <div class="resize-handle" onmousedown="startResize(event, 'tenant')"></div>
            </div>
            <div class="col-jobtype" onclick="sortBy('jobType')">
                JOB TYPE
                <div class="resize-handle" onmousedown="startResize(event, 'jobtype')"></div>
            </div>
            <div class="col-duration" onclick="sortBy('duration')">
                DURATION
                <div class="resize-handle" onmousedown="startResize(event, 'duration')"></div>
            </div>
            <div class="col-operation" onclick="sortBy('operation')">
                OPERATION
                <div class="resize-handle" onmousedown="startResize(event, 'operation')"></div>
            </div>
            <div class="col-stream" onclick="sortBy('logStream')">
                LOG STREAM
                <div class="resize-handle" onmousedown="startResize(event, 'stream')"></div>
            </div>
        </div>
        <div class="timeline-content" id="timelineContent"></div>
    </div>

    <script>
        let allJobs = [];
        let filteredJobs = [];
        let currentSort = { field: 'time', direction: 'asc' };
        let isResizing = false;
        let currentResizeColumn = null;

        // Initialize grid columns
        updateGridColumns();

        document.getElementById('fileInput').addEventListener('change', handleFile);

        // Drag and drop
        const uploadArea = document.querySelector('.upload-area');
        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.style.background = '#f0f8ff';
        });
        uploadArea.addEventListener('dragleave', () => {
            uploadArea.style.background = 'white';
        });
        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.style.background = 'white';
            const files = e.dataTransfer.files;
            if (files.length > 0) {
                processFile(files[0]);
            }
        });

        function handleFile(event) {
            const file = event.target.files[0];
            if (file) {
                processFile(file);
            }
        }

        function processFile(file) {
            const reader = new FileReader();
            reader.onload = function(e) {
                try {
                    const data = JSON.parse(e.target.result);
                    parseLogData(data);
                } catch (error) {
                    alert('Error parsing JSON file: ' + error.message);
                }
            };
            reader.readAsText(file);
        }

        function parseLogData(data) {
            allJobs = [];
            
            // Check if data has events array
            if (!data || !data.events || !Array.isArray(data.events)) {
                alert('Invalid JSON format. Expected CloudWatch logs with events array.');
                return;
            }
            
            data.events.forEach(event => {
                try {
                    const timestamp = new Date(event.timestamp);
                    const message = event.message;
                    
                    // Parse TimedAspect log messages
                    const match = message.match(/TimedAspect - (.+?) executed in (.+?) on (.+?) by/);
                    if (match) {
                        const [, operation, duration, tenant] = match;
                        
                        // Determine job type
                        let jobType = 'Other';
                        if (operation.includes('ClearingHouse')) jobType = 'ClearingHouse';
                        else if (operation.includes('GeneralLedger')) jobType = 'GeneralLedger';
                        else if (operation.includes('ClinicalOperations')) jobType = 'ClinicalOperations';
                        else if (operation.includes('QuicksightExport')) jobType = 'QuicksightExport';
                        
                        const durationMinutes = parseDuration(duration);
                        
                        allJobs.push({
                            timestamp,
                            time: convertToEST(timestamp),
                            operation,
                            duration,
                            durationMinutes,
                            tenant,
                            jobType,
                            logStream: event.logStreamName || 'unknown'
                        });
                    }
                } catch (error) {
                    console.warn('Error parsing event:', error, event);
                }
            });
            
            if (allJobs.length === 0) {
                alert('No job execution logs found in the data. Make sure you filtered for "executed in" pattern.');
                return;
            }
            
            allJobs.sort((a, b) => a.timestamp - b.timestamp);
            filteredJobs = [...allJobs];
            
            try {
                displayStats();
                displayChart();
                displayTimeline();
                
                // Safely show UI elements
                const statsEl = document.getElementById('stats');
                const chartEl = document.getElementById('chartContainer');
                const filtersEl = document.getElementById('filterControls');
                const timelineEl = document.getElementById('timeline');
                
                if (statsEl) statsEl.style.display = 'grid';
                if (chartEl) chartEl.style.display = 'block';
                if (filtersEl) filtersEl.style.display = 'flex';
                if (timelineEl) timelineEl.style.display = 'block';
                
                // Show color legend
                const legendEl = document.getElementById('colorLegend');
                if (legendEl) legendEl.style.display = 'block';
                
            } catch (error) {
                alert('Error displaying data: ' + error.message);
                console.error('Display error:', error);
            }
        }

        function convertToEST(timestamp) {
            // Convert UTC timestamp to EST (UTC-5) or EDT (UTC-4)
            // Use the original timestamp for proper time display
            const date = new Date(timestamp);

            // Format as HH:MM:SS in local time (which should be more accurate)
            return date.toLocaleTimeString('en-US', {
                hour12: false,
                timeZone: 'America/New_York'
            });
        }

        function parseDuration(duration) {
            const parts = duration.split(', ');
            let totalMinutes = 0;
            
            parts.forEach(part => {
                if (part.includes('min')) {
                    totalMinutes += parseInt(part);
                } else if (part.includes('sec')) {
                    totalMinutes += parseInt(part) / 60;
                }
            });
            
            return totalMinutes;
        }

        function displayStats() {
            if (allJobs.length === 0) return;
            
            const totalJobs = allJobs.length;
            const uniqueTenants = new Set(allJobs.map(job => job.tenant)).size;
            const longestJob = allJobs.reduce((max, job) => 
                job.durationMinutes > max.durationMinutes ? job : max, allJobs[0]);
            
            // Time range calculation
            const firstJob = allJobs[0];
            const lastJob = allJobs[allJobs.length - 1];
            const timeRange = `${firstJob.time} - ${lastJob.time}`;
            
            // Peak hour calculation
            const hourCounts = {};
            allJobs.forEach(job => {
                const hour = job.time.substr(0, 2);
                hourCounts[hour] = (hourCounts[hour] || 0) + 1;
            });
            const peakHour = Object.entries(hourCounts).reduce((max, [hour, count]) => 
                count > max.count ? {hour, count} : max, {hour: '00', count: 0});
            
            // Safely update elements
            const elements = {
                'totalJobs': totalJobs,
                'uniqueTenants': uniqueTenants,
                'longestJob': `${longestJob.duration} (${longestJob.tenant})`,
                'timeRange': timeRange,
                'peakHour': `${peakHour.hour}:XX (${peakHour.count} jobs)`
            };
            
            for (const [id, value] of Object.entries(elements)) {
                const el = document.getElementById(id);
                if (el) el.textContent = value;
            }
        }

        function displayChart() {
            const chart = document.getElementById('timelineChart');
            const tooltip = document.getElementById('chartTooltip');
            chart.innerHTML = '';
            
            // Group jobs by hour (EST)
            const hourCounts = {};
            allJobs.forEach(job => {
                const hour = parseInt(job.time.substr(0, 2));
                if (!hourCounts[hour]) {
                    hourCounts[hour] = { count: 0, jobs: [] };
                }
                hourCounts[hour].count++;
                hourCounts[hour].jobs.push(job);
            });
            
            // Find max count for scaling (ensure minimum height for visibility)
            const maxCount = Math.max(...Object.values(hourCounts).map(h => h.count), 1);
            
            // Create bars for each hour (0-23)
            for (let hour = 0; hour < 24; hour++) {
                const count = hourCounts[hour] ? hourCounts[hour].count : 0;
                const jobs = hourCounts[hour] ? hourCounts[hour].jobs : [];
                // Ensure minimum height of 5px for visibility, max 180px
                const height = count === 0 ? 0 : Math.max(5, (count / maxCount) * 180);
                
                const bar = document.createElement('div');
                bar.className = 'chart-bar';
                if (count > maxCount * 0.7) bar.className += ' high-load';
                else if (count > maxCount * 0.4) bar.className += ' medium-load';
                
                bar.style.left = `${(hour / 24) * 100}%`;
                bar.style.width = `${(1 / 24) * 100 - 1}%`; // Slightly less width for spacing
                bar.style.height = `${height}px`;
                
                // Add hover tooltip
                bar.addEventListener('mouseenter', (e) => {
                    tooltip.style.display = 'block';
                    tooltip.style.left = e.pageX + 10 + 'px';
                    tooltip.style.top = e.pageY - 30 + 'px';
                    tooltip.innerHTML = `
                        ${hour.toString().padStart(2, '0')}:00 EST - ${count} jobs<br>
                        ${jobs.length > 0 ? 'Top tenants: ' + [...new Set(jobs.map(j => j.tenant))].slice(0, 3).join(', ') : 'No jobs'}
                    `;
                });
                
                bar.addEventListener('mouseleave', () => {
                    tooltip.style.display = 'none';
                });
                
                chart.appendChild(bar);
                
                // Add hour labels (show every 2 hours to avoid crowding)
                if (hour % 2 === 0) {
                    const label = document.createElement('div');
                    label.className = 'chart-axis';
                    label.style.left = `${(hour / 24) * 100 + (1/48)*100}%`;
                    label.textContent = hour.toString().padStart(2, '0') + ':00';
                    chart.appendChild(label);
                }
            }
        }

        function sortBy(field) {
            if (filteredJobs.length === 0) return;
            
            // Update sort direction
            if (currentSort.field === field) {
                currentSort.direction = currentSort.direction === 'asc' ? 'desc' : 'asc';
            } else {
                currentSort.field = field;
                currentSort.direction = 'asc';
            }
            
            // Update header indicators
            document.querySelectorAll('.timeline-header div').forEach(header => {
                header.classList.remove('sorted', 'asc', 'desc');
            });

            // Map field names to column classes
            const fieldToColumn = {
                'time': 'col-time',
                'tenant': 'col-tenant',
                'jobType': 'col-jobtype',
                'duration': 'col-duration',
                'operation': 'col-operation',
                'logStream': 'col-stream'
            };

            const header = document.querySelector(`.${fieldToColumn[field]}`);
            if (header) {
                header.classList.add('sorted', currentSort.direction);
            }
            
            // Sort the data
            filteredJobs.sort((a, b) => {
                let aVal = a[field];
                let bVal = b[field];

                // Special handling for different field types
                if (field === 'duration') {
                    aVal = a.durationMinutes;
                    bVal = b.durationMinutes;
                } else if (field === 'time') {
                    // Use timestamp for proper chronological sorting
                    aVal = a.timestamp.getTime();
                    bVal = b.timestamp.getTime();
                }

                // Handle string comparisons
                if (typeof aVal === 'string') {
                    aVal = aVal.toLowerCase();
                    bVal = bVal.toLowerCase();
                }

                // Perform comparison
                let result;
                if (aVal < bVal) {
                    result = -1;
                } else if (aVal > bVal) {
                    result = 1;
                } else {
                    result = 0;
                }

                // Apply sort direction
                return currentSort.direction === 'asc' ? result : -result;
            });
            
            displayTimeline();
        }

        function displayTimeline() {
            const timelineContent = document.getElementById('timelineContent');
            timelineContent.innerHTML = '';

            // Ensure grid columns are properly set
            updateGridColumns();

            filteredJobs.forEach(job => {
                const entry = document.createElement('div');
                entry.className = `job-entry ${getJobClass(job)}`;
                
                // Extract short stream name (last part)
                const shortStream = job.logStream.split('/').pop().substr(0, 8) + '...';
                
                entry.innerHTML = `
                    <div class="col-time">${job.time}</div>
                    <div class="col-tenant">${job.tenant}</div>
                    <div class="col-jobtype">${job.jobType}</div>
                    <div class="col-duration">${job.duration}</div>
                    <div class="col-operation" title="${job.operation}">${job.operation.replace(/.*\./, '')}</div>
                    <div class="col-stream" style="font-size: 10px; color: #999;" title="${job.logStream}">${shortStream}</div>
                `;
                
                timelineContent.appendChild(entry);
            });
            
            // Update results count
            if (document.getElementById('resultsCount')) {
                document.getElementById('resultsCount').textContent = `Showing ${filteredJobs.length} of ${allJobs.length} jobs`;
            } else {
                const resultsDiv = document.createElement('div');
                resultsDiv.id = 'resultsCount';
                resultsDiv.style.cssText = 'margin: 10px 0; font-weight: bold; color: #666;';
                resultsDiv.textContent = `Showing ${filteredJobs.length} of ${allJobs.length} jobs`;
                document.getElementById('timeline').insertBefore(resultsDiv, document.getElementById('timeline').firstChild.nextSibling);
            }
        }

        function getJobClass(job) {
            if (job.durationMinutes > 30) return 'long-duration';
            if (job.durationMinutes > 5) return 'medium-duration';
            if (job.jobType === 'ClearingHouse') return 'clearing';
            if (job.jobType === 'ClinicalOperations') return 'clinical';
            if (job.jobType === 'QuicksightExport') return 'quicksight';
            return 'normal';
        }

        function applyFilters() {
            const tenantFilter = document.getElementById('tenantFilter').value.toLowerCase();
            const jobTypeFilter = document.getElementById('jobTypeFilter').value;
            const minDuration = parseFloat(document.getElementById('minDuration').value) || 0;
            const streamFilter = document.getElementById('streamFilter').value.toLowerCase();
            
            filteredJobs = allJobs.filter(job => {
                if (tenantFilter && !job.tenant.toLowerCase().includes(tenantFilter)) return false;
                if (jobTypeFilter && job.jobType !== jobTypeFilter) return false;
                if (job.durationMinutes < minDuration) return false;
                if (streamFilter && !job.logStream.toLowerCase().includes(streamFilter)) return false;
                return true;
            });
            
            displayTimeline();
        }

        function clearFilters() {
            document.getElementById('tenantFilter').value = '';
            document.getElementById('jobTypeFilter').value = '';
            document.getElementById('minDuration').value = '';
            document.getElementById('streamFilter').value = '';
            filteredJobs = [...allJobs];
            displayTimeline();
        }

        function exportFilteredData() {
            if (filteredJobs.length === 0) {
                alert('No data to export');
                return;
            }
            
            const csvHeader = 'Timestamp,Tenant,JobType,Duration,DurationMinutes,Operation,LogStream\n';
            const csvData = filteredJobs.map(job => 
                `${job.timestamp.toISOString()},${job.tenant},${job.jobType},"${job.duration}",${job.durationMinutes},"${job.operation}",${job.logStream}`
            ).join('\n');
            
            const blob = new Blob([csvHeader + csvData], { type: 'text/csv' });
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.style.display = 'none';
            a.href = url;
            a.download = 'cloudwatch_timeline.csv';
            document.body.appendChild(a);
            a.click();
            window.URL.revokeObjectURL(url);
            document.body.removeChild(a);
        }

        // Column width management - fixed widths for proper layout
        let columnWidths = {
            time: 150,
            tenant: 180,
            jobtype: 140,
            duration: 120,
            operation: 400,
            stream: 150
        };

        let isCustomWidths = false;

        function updateGridColumns() {
            const gridTemplate = Object.values(columnWidths).map(w => w + 'px').join(' ');

            const header = document.querySelector('.timeline-header');
            if (header) {
                header.style.gridTemplateColumns = gridTemplate;
            }
            document.querySelectorAll('.job-entry').forEach(entry => {
                entry.style.gridTemplateColumns = gridTemplate;
            });
        }

        function startResize(e, column) {
            e.stopPropagation();
            e.preventDefault();
            isResizing = true;
            currentResizeColumn = column;
            document.body.classList.add('resizing');

            const startX = e.clientX;
            const startWidth = columnWidths[column];

            function doResize(e) {
                if (!isResizing) return;
                const deltaX = e.clientX - startX;
                const newWidth = Math.max(80, startWidth + deltaX);

                columnWidths[column] = newWidth;
                updateGridColumns();
            }

            function stopResize() {
                isResizing = false;
                currentResizeColumn = null;
                document.body.classList.remove('resizing');
                document.removeEventListener('mousemove', doResize);
                document.removeEventListener('mouseup', stopResize);
            }

            document.addEventListener('mousemove', doResize);
            document.addEventListener('mouseup', stopResize);
        }
    </script>
</body>
</html>