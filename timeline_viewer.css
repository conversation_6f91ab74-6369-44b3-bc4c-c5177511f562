/* AWS CloudWatch Log Timeline Analyzer Styles */

body { 
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif; 
    margin: 20px; 
    background: #f5f5f5; 
    color: #333; 
}

.header { 
    text-align: center; 
    margin-bottom: 15px; 
    padding: 10px;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.header h1 { 
    margin: 0 0 5px 0; 
    font-size: 20px; 
}

.header p { 
    margin: 0; 
    font-size: 14px; 
    color: #666; 
}

.upload-area { 
    border: 2px dashed #007acc; 
    padding: 15px; 
    text-align: center; 
    margin-bottom: 15px; 
    cursor: pointer;
    background: white;
    border-radius: 8px;
    transition: background 0.3s;
}

.upload-area h3 { 
    margin: 0 0 5px 0; 
    font-size: 16px; 
}

.upload-area p { 
    margin: 0; 
    font-size: 12px; 
    color: #666; 
}

.upload-area:hover { 
    background: #f0f8ff; 
}

/* Timeline Styles */
.timeline { 
    max-height: 600px; 
    overflow-y: auto; 
    border: 1px solid #ddd; 
    background: white;
    border-radius: 8px;
    font-family: 'Courier New', monospace;
    font-size: 13px;
}

.timeline-header {
    background: #f8f9fa;
    padding: 10px 15px;
    border-bottom: 2px solid #ddd;
    font-weight: bold;
    position: sticky;
    top: 0;
    z-index: 10;
    display: grid;
    grid-template-columns: 120px 140px 120px 100px 300px 120px;
    gap: 15px;
    font-size: 12px;
    color: #666;
}

.timeline-header div {
    cursor: pointer;
    user-select: none;
    position: relative;
    padding-right: 20px;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.timeline-header div:hover {
    background: #e9ecef;
    border-radius: 3px;
}

/* Resize Handle Styles */
.resize-handle {
    position: absolute;
    right: 0;
    top: 0;
    bottom: 0;
    width: 8px;
    cursor: col-resize;
    background: transparent;
    border-right: 2px solid transparent;
    transition: border-color 0.2s;
}

.resize-handle:hover {
    border-right-color: #007acc;
}

.resizing {
    cursor: col-resize !important;
}

.resizing * {
    cursor: col-resize !important;
}

/* Sort Indicators */
.timeline-header div.sorted::after {
    content: '';
    position: absolute;
    right: 5px;
    top: 50%;
    transform: translateY(-50%);
    width: 0;
    height: 0;
    border-left: 4px solid transparent;
    border-right: 4px solid transparent;
}

.timeline-header div.sorted.asc::after {
    border-bottom: 6px solid #666;
}

.timeline-header div.sorted.desc::after {
    border-top: 6px solid #666;
}

/* Chart Styles */
.chart-container {
    margin: 20px 0;
    background: white;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 20px;
    display: none;
}

.chart-title {
    text-align: center;
    font-weight: bold;
    margin-bottom: 15px;
    color: #333;
}

.timeline-chart {
    height: 200px;
    position: relative;
    border: 1px solid #ddd;
    background: linear-gradient(to right, #f8f9fa 0%, #ffffff 100%);
    overflow: hidden;
}

.chart-bar {
    position: absolute;
    bottom: 0;
    background: #007acc;
    border-radius: 2px 2px 0 0;
    transition: all 0.3s ease;
    cursor: pointer;
}

.chart-bar:hover {
    background: #005a9e;
}

.chart-bar.high-load {
    background: #dc3545;
}

.chart-bar.medium-load {
    background: #ffc107;
}

.chart-axis {
    position: absolute;
    bottom: -25px;
    font-size: 10px;
    color: #666;
    transform: translateX(-50%);
}

.chart-tooltip {
    position: absolute;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 5px 8px;
    border-radius: 4px;
    font-size: 11px;
    pointer-events: none;
    z-index: 1000;
    display: none;
}

/* Timeline Content */
.timeline-content {
    padding: 15px;
}

.job-entry { 
    margin: 2px 0; 
    padding: 8px 15px; 
    border-left: 4px solid #ddd;
    background: #fafafa;
    border-radius: 3px;
    display: grid;
    grid-template-columns: 120px 140px 120px 100px 300px 120px;
    gap: 15px;
    align-items: center;
    font-size: 12px;
}

.job-entry .col-operation {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

/* Job Type Colors */
.normal { border-left-color: #28a745; }
.long-duration { border-left-color: #dc3545; background: #fff5f5; }
.medium-duration { border-left-color: #ffc107; background: #fffbf0; }
.clearing { border-left-color: #007bff; background: #f0f8ff; }
.clinical { border-left-color: #fd7e14; background: #fff8f0; }
.quicksight { border-left-color: #20c997; background: #f0fff8; }

/* Stats Grid */
.stats { 
    display: grid; 
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); 
    gap: 15px; 
    margin-bottom: 20px; 
}

.stat-box { 
    background: white;
    border: 1px solid #ddd; 
    padding: 20px; 
    text-align: center;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.stat-value { 
    font-size: 24px; 
    font-weight: bold; 
    color: #007acc; 
}

/* Filter Controls */
.filter-controls { 
    margin: 15px 0; 
    padding: 12px; 
    background: white;
    border: 1px solid #ddd;
    border-radius: 8px;
    display: flex;
    gap: 10px;
    flex-wrap: wrap;
    align-items: center;
    font-size: 12px;
}

.filter-controls input, .filter-controls select { 
    padding: 4px 8px; 
    border: 1px solid #ddd; 
    border-radius: 4px;
    font-size: 12px;
}

.filter-controls button {
    background: #007acc;
    color: white;
    border: none;
    padding: 4px 12px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
}

.filter-controls button:hover { 
    background: #005a9e; 
}

/* Color Legend */
.color-legend {
    background: white;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 10px;
    margin: 10px 0;
    font-size: 11px;
}

.legend-item {
    display: inline-block;
    margin: 2px 8px;
}

.legend-color {
    display: inline-block;
    width: 12px;
    height: 12px;
    border-radius: 2px;
    margin-right: 4px;
    vertical-align: middle;
}

/* Text Styling */
.timestamp { color: #666; font-weight: bold; }
.duration { font-weight: bold; }
.tenant { color: #007acc; font-weight: bold; }
.operation { color: #555; }
