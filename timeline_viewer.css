/* AWS CloudWatch Log Timeline Analyzer Styles */

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    margin: 0;
    padding: 10px;
    background: #f5f5f5;
    color: #333;
    width: calc(100vw - 20px);
    height: calc(100vh - 20px);
    max-width: none;
    overflow: hidden;
    box-sizing: border-box;
}

.header {
    text-align: center;
    margin-bottom: 8px;
    padding: 8px;
    background: white;
    border-radius: 6px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.header h1 {
    margin: 0 0 3px 0;
    font-size: 18px;
}

.header p {
    margin: 0;
    font-size: 12px;
    color: #666;
}

.upload-area {
    border: 2px dashed #007acc;
    padding: 10px;
    text-align: center;
    margin-bottom: 8px;
    cursor: pointer;
    background: white;
    border-radius: 6px;
    transition: background 0.3s;
}

.upload-area h3 {
    margin: 0 0 3px 0;
    font-size: 14px;
}

.upload-area p {
    margin: 0;
    font-size: 11px;
    color: #666;
}

.upload-area:hover { 
    background: #f0f8ff; 
}

/* Timeline Styles */
.timeline {
    height: calc(100vh - 320px);
    overflow-y: auto;
    overflow-x: auto;
    border: 1px solid #ddd;
    background: white;
    border-radius: 6px;
    font-family: 'Courier New', monospace;
    font-size: 12px;
    width: 100%;
    min-width: 1270px;
}

.timeline-header {
    background: #f8f9fa;
    padding: 10px 15px;
    border-bottom: 2px solid #ddd;
    font-weight: bold;
    position: sticky;
    top: 0;
    z-index: 10;
    display: grid;
    grid-template-columns: 180px 180px 140px 100px 120px 350px 120px;
    gap: 10px;
    font-size: 12px;
    color: #666;
    width: 100%;
    min-width: 1270px;
    box-sizing: border-box;
}

.timeline-header div {
    cursor: pointer;
    user-select: none;
    position: relative;
    padding: 5px 20px 5px 5px;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    min-width: 0;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.timeline-header div:hover {
    background: #e9ecef;
    border-radius: 3px;
}

/* Resize Handle Styles */
.resize-handle {
    position: absolute;
    right: -5px;
    top: 0;
    bottom: 0;
    width: 10px;
    cursor: col-resize;
    background: transparent;
    transition: all 0.2s;
    z-index: 20;
}

.resize-handle:hover {
    background: rgba(0, 122, 204, 0.2);
    border-right: 2px solid #007acc;
}

.resize-handle::after {
    content: '';
    position: absolute;
    right: 4px;
    top: 50%;
    transform: translateY(-50%);
    width: 2px;
    height: 20px;
    background: #ccc;
    transition: background 0.2s;
}

.resize-handle:hover::after {
    background: #007acc;
}

.resizing {
    cursor: col-resize !important;
}

.resizing * {
    cursor: col-resize !important;
}

/* Sort Indicators */
.timeline-header div.sorted::after {
    content: '';
    position: absolute;
    right: 5px;
    top: 50%;
    transform: translateY(-50%);
    width: 0;
    height: 0;
    border-left: 4px solid transparent;
    border-right: 4px solid transparent;
}

.timeline-header div.sorted.asc::after {
    border-bottom: 6px solid #666;
}

.timeline-header div.sorted.desc::after {
    border-top: 6px solid #666;
}

/* Chart Styles */
.chart-container {
    margin: 8px 0;
    background: white;
    border: 1px solid #ddd;
    border-radius: 6px;
    padding: 12px;
    display: none;
    width: 100%;
    box-sizing: border-box;
}

.chart-title {
    text-align: center;
    font-weight: bold;
    margin-bottom: 15px;
    color: #333;
}

.timeline-chart {
    height: 120px;
    position: relative;
    border: 1px solid #ddd;
    background: linear-gradient(to right, #f8f9fa 0%, #ffffff 100%);
    overflow: hidden;
    width: 100%;
    box-sizing: border-box;
}

.chart-bar {
    position: absolute;
    bottom: 0;
    background: #007acc;
    border-radius: 2px 2px 0 0;
    transition: all 0.3s ease;
    cursor: pointer;
}

.chart-bar:hover {
    background: #005a9e;
}

.chart-bar.high-load {
    background: #dc3545;
}

.chart-bar.medium-load {
    background: #ffc107;
}

.chart-axis {
    position: absolute;
    bottom: -25px;
    font-size: 10px;
    color: #666;
    transform: translateX(-50%);
}

.chart-tooltip {
    position: absolute;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 5px 8px;
    border-radius: 4px;
    font-size: 11px;
    pointer-events: none;
    z-index: 1000;
    display: none;
}

/* Timeline Content */
.timeline-content {
    padding: 15px;
    width: 100%;
    box-sizing: border-box;
}

.job-entry {
    margin: 2px 0;
    padding: 8px 15px;
    border-left: 4px solid #ddd;
    background: #fafafa;
    border-radius: 3px;
    display: grid;
    grid-template-columns: 180px 180px 140px 100px 120px 350px 120px;
    gap: 10px;
    align-items: center;
    font-size: 12px;
    width: 100%;
    min-width: 1270px;
    box-sizing: border-box;
}

.job-entry > div {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    min-width: 0;
    padding: 2px 4px;
}

/* Job Type Colors */
.normal { border-left-color: #28a745; }
.long-duration { border-left-color: #dc3545; background: #fff5f5; }
.medium-duration { border-left-color: #ffc107; background: #fffbf0; }
.clearing { border-left-color: #007bff; background: #f0f8ff; }
.clinical { border-left-color: #fd7e14; background: #fff8f0; }
.quicksight { border-left-color: #20c997; background: #f0fff8; }
.failed { border-left-color: #dc3545; background: #fff5f5; }

/* Status Indicators */
.status-completed {
    color: #28a745;
    font-weight: bold;
}
.status-failed {
    color: #dc3545;
    font-weight: bold;
}
.status-unknown {
    color: #6c757d;
    font-weight: bold;
}

/* Stats Grid */
.stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: 8px;
    margin-bottom: 8px;
}

.stat-box {
    background: white;
    border: 1px solid #ddd;
    padding: 12px;
    text-align: center;
    border-radius: 6px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.stat-value {
    font-size: 18px;
    font-weight: bold;
    color: #007acc;
}

/* Filter Controls */
.filter-controls {
    margin: 8px 0;
    padding: 8px;
    background: white;
    border: 1px solid #ddd;
    border-radius: 6px;
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
    align-items: center;
    font-size: 11px;
}

.filter-controls input, .filter-controls select { 
    padding: 4px 8px; 
    border: 1px solid #ddd; 
    border-radius: 4px;
    font-size: 12px;
}

.filter-controls button {
    background: #007acc;
    color: white;
    border: none;
    padding: 4px 12px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
}

.filter-controls button:hover { 
    background: #005a9e; 
}

/* Color Legend */
.color-legend {
    background: white;
    border: 1px solid #ddd;
    border-radius: 6px;
    padding: 6px;
    margin: 6px 0;
    font-size: 10px;
}

.legend-item {
    display: inline-block;
    margin: 2px 8px;
}

.legend-color {
    display: inline-block;
    width: 12px;
    height: 12px;
    border-radius: 2px;
    margin-right: 4px;
    vertical-align: middle;
}

/* Text Styling */
.timestamp { color: #666; font-weight: bold; }
.duration { font-weight: bold; }
.tenant { color: #007acc; font-weight: bold; }
.operation { color: #555; }
