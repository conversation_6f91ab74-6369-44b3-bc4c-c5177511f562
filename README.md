# AWS CloudWatch Log Timeline Analyzer

An improved log viewer with resizable columns for analyzing AWS CloudWatch job execution logs.

## 🆕 Recent Improvements

### ✅ Resizable Columns
- **Fixed the super wide OPERATION column** - now has a reasonable default width of 300px
- **Added resize handles** to all column headers - hover over the right edge of any column header to see the resize cursor
- **Drag to resize** - click and drag the resize handles to adjust column widths
- **Visual feedback** - resize handles highlight on hover and cursor changes during resize operations
- **Minimum width protection** - columns can't be resized smaller than 50px

### ✅ Better Column Layout
- **Improved default widths**:
  - TIME: 120px (was 80px)
  - TENANT: 140px (was 120px) 
  - JOB TYPE: 120px (was 100px)
  - DURATION: 100px (was 80px)
  - OPERATION: 300px (was taking all remaining space with `1fr`)
  - LOG STREAM: 120px (was 100px)

### ✅ Code Organization
- **Separated into multiple files** for better maintainability:
  - `timeline_viewer_modular.html` - Clean HTML structure
  - `timeline_viewer.css` - All styles
  - `timeline_viewer.js` - All JavaScript functionality
  - `timeline_viewer.html` - Original single-file version (still works)

### ✅ Enhanced User Experience
- **Better text overflow handling** - Operation column now shows ellipsis for long text
- **Improved hover states** - Visual feedback for interactive elements
- **Consistent spacing** - Better gap management between columns

## 🚀 How to Use Resizable Columns

1. **Load your CloudWatch JSON data** using the file upload area
2. **Hover over column headers** - you'll see resize handles appear on the right edge
3. **Click and drag** the resize handles to adjust column widths
4. **Release** to set the new width
5. **All rows update** automatically to match the new column layout

## 📁 File Structure

```
├── timeline_viewer_modular.html    # New modular version (recommended)
├── timeline_viewer.css             # Stylesheet
├── timeline_viewer.js              # JavaScript functionality  
├── timeline_viewer.html            # Original single-file version
└── README.md                       # This documentation
```

## 🎯 Features

- **Drag & Drop** file upload
- **Resizable columns** with visual feedback
- **Sortable columns** - click headers to sort
- **Filtering** by tenant, job type, duration, and log stream
- **Color-coded job types** and duration categories
- **Interactive timeline chart** showing jobs per hour
- **CSV export** of filtered data
- **Statistics dashboard** with key metrics

## 🔧 Technical Details

### Column Resize Implementation
- Uses CSS Grid for layout consistency
- JavaScript event handling for mouse interactions
- Dynamic grid template updates
- Minimum width constraints for usability

### Browser Compatibility
- Modern browsers with CSS Grid support
- ES6+ JavaScript features
- File API for drag & drop uploads

## 🐛 Known Issues Fixed

- ✅ OPERATION column no longer takes excessive width
- ✅ Columns are now properly resizable
- ✅ Better visual feedback for interactive elements
- ✅ Improved code organization and maintainability

## 💡 Usage Tips

1. **Start with the modular version** (`timeline_viewer_modular.html`) for the best experience
2. **Adjust the OPERATION column** first - it's usually the one that needs the most adjustment
3. **Use the tooltip** on operation cells to see the full text when truncated
4. **Resize columns before filtering** for the best layout experience

## 🔄 Migration from Original

If you were using the original `timeline_viewer.html`, you can:
1. Continue using it (it now has resizable columns too)
2. Switch to the modular version for better organization
3. Both versions have identical functionality

The modular version is recommended for easier maintenance and customization.
