// AWS CloudWatch Log Timeline Analyzer JavaScript

let allJobs = [];
let filteredJobs = [];
let currentSort = { field: 'time', direction: 'asc' };
let isResizing = false;
let currentResizeColumn = null;

// Column width management - fixed widths for proper layout
let columnWidths = {
    time: 180,
    tenant: 180,
    jobtype: 140,
    duration: 100,
    status: 120,
    operation: 350,
    stream: 120
};

let isCustomWidths = false;

// Initialize event listeners
document.addEventListener('DOMContentLoaded', function() {
    // Initialize grid columns
    updateGridColumns();

    document.getElementById('fileInput').addEventListener('change', handleFile);

    // Drag and drop
    const uploadArea = document.querySelector('.upload-area');
    uploadArea.addEventListener('dragover', (e) => {
        e.preventDefault();
        uploadArea.style.background = '#f0f8ff';
    });
    uploadArea.addEventListener('dragleave', () => {
        uploadArea.style.background = 'white';
    });
    uploadArea.addEventListener('drop', (e) => {
        e.preventDefault();
        uploadArea.style.background = 'white';
        const files = e.dataTransfer.files;
        if (files.length > 0) {
            processFile(files[0]);
        }
    });
});

function handleFile(event) {
    const file = event.target.files[0];
    if (file) {
        processFile(file);
    }
}

function processFile(file) {
    const reader = new FileReader();
    reader.onload = function(e) {
        try {
            const data = JSON.parse(e.target.result);
            parseLogData(data);
        } catch (error) {
            alert('Error parsing JSON file: ' + error.message);
        }
    };
    reader.readAsText(file);
}

function parseLogData(data) {
    allJobs = [];
    
    // Check if data has events array
    if (!data || !data.events || !Array.isArray(data.events)) {
        alert('Invalid JSON format. Expected CloudWatch logs with events array.');
        return;
    }
    
    data.events.forEach(event => {
        try {
            const timestamp = new Date(event.timestamp);
            const message = event.message;
            
            // Parse TimedAspect log messages
            const completedMatch = message.match(/TimedAspect - (.+?) executed in (.+?) on (.+?) by/);
            const failedMatch = message.match(/TimedAspect.*(?:failed|error|exception)/i);
            const startedMatch = message.match(/TimedAspect.*(?:starting|began|initiated)/i);

            if (completedMatch) {
                // Completed job
                const [, operation, duration, tenant] = completedMatch;

                // Determine job type
                let jobType = 'Other';
                if (operation.includes('ClearingHouse')) jobType = 'ClearingHouse';
                else if (operation.includes('GeneralLedger')) jobType = 'GeneralLedger';
                else if (operation.includes('ClinicalOperations')) jobType = 'ClinicalOperations';
                else if (operation.includes('QuicksightExport')) jobType = 'QuicksightExport';

                const durationMinutes = parseDuration(duration);

                allJobs.push({
                    timestamp,
                    time: convertToEST(timestamp),
                    operation,
                    duration,
                    durationMinutes,
                    tenant,
                    jobType,
                    status: 'Completed',
                    statusClass: 'status-completed',
                    logStream: event.logStreamName || 'unknown'
                });
            } else if (failedMatch || message.includes('Exception') || message.includes('ERROR')) {
                // Failed job - extract what we can
                const operationMatch = message.match(/TimedAspect.*?([A-Z][a-zA-Z]+(?:Job|Task|Operation|Service))/);
                const tenantMatch = message.match(/on ([a-zA-Z0-9_-]+)/);

                const operation = operationMatch ? operationMatch[1] : 'Unknown Operation';
                const tenant = tenantMatch ? tenantMatch[1] : 'Unknown';

                // Determine job type
                let jobType = 'Other';
                if (operation.includes('ClearingHouse')) jobType = 'ClearingHouse';
                else if (operation.includes('GeneralLedger')) jobType = 'GeneralLedger';
                else if (operation.includes('ClinicalOperations')) jobType = 'ClinicalOperations';
                else if (operation.includes('QuicksightExport')) jobType = 'QuicksightExport';

                allJobs.push({
                    timestamp,
                    time: convertToEST(timestamp),
                    operation,
                    duration: 'Failed',
                    durationMinutes: 0,
                    tenant,
                    jobType,
                    status: 'Failed',
                    statusClass: 'status-failed',
                    logStream: event.logStreamName || 'unknown'
                });
            } else if (message.includes('TimedAspect')) {
                // Other TimedAspect messages - might be start events or other info
                const operationMatch = message.match(/TimedAspect.*?([A-Z][a-zA-Z]+(?:Job|Task|Operation|Service))/);
                const tenantMatch = message.match(/on ([a-zA-Z0-9_-]+)/);

                const operation = operationMatch ? operationMatch[1] : 'Unknown Operation';
                const tenant = tenantMatch ? tenantMatch[1] : 'Unknown';

                // Only add if we can extract meaningful info
                if (operation !== 'Unknown Operation') {
                    let jobType = 'Other';
                    if (operation.includes('ClearingHouse')) jobType = 'ClearingHouse';
                    else if (operation.includes('GeneralLedger')) jobType = 'GeneralLedger';
                    else if (operation.includes('ClinicalOperations')) jobType = 'ClinicalOperations';
                    else if (operation.includes('QuicksightExport')) jobType = 'QuicksightExport';

                    allJobs.push({
                        timestamp,
                        time: convertToEST(timestamp),
                        operation,
                        duration: 'Unknown',
                        durationMinutes: 0,
                        tenant,
                        jobType,
                        status: 'Unknown',
                        statusClass: 'status-unknown',
                        logStream: event.logStreamName || 'unknown'
                    });
                }
            }
        } catch (error) {
            console.warn('Error parsing event:', error, event);
        }
    });
    
    if (allJobs.length === 0) {
        alert('No job execution logs found in the data. Make sure you filtered for "executed in" pattern.');
        return;
    }
    
    allJobs.sort((a, b) => a.timestamp - b.timestamp);
    filteredJobs = [...allJobs];
    
    try {
        displayStats();
        displayChart();
        displayTimeline();
        
        // Safely show UI elements
        const statsEl = document.getElementById('stats');
        const chartEl = document.getElementById('chartContainer');
        const filtersEl = document.getElementById('filterControls');
        const timelineEl = document.getElementById('timeline');
        
        if (statsEl) statsEl.style.display = 'grid';
        if (chartEl) chartEl.style.display = 'block';
        if (filtersEl) filtersEl.style.display = 'flex';
        if (timelineEl) timelineEl.style.display = 'block';
        
        // Show color legend
        const legendEl = document.getElementById('colorLegend');
        if (legendEl) legendEl.style.display = 'block';
        
    } catch (error) {
        alert('Error displaying data: ' + error.message);
        console.error('Display error:', error);
    }
}

function convertToEST(timestamp) {
    // Convert UTC timestamp to EST (UTC-5) or EDT (UTC-4)
    // Use the original timestamp for proper time display
    const date = new Date(timestamp);

    // Format as HH:MM:SS AM/PM in Eastern time
    return date.toLocaleTimeString('en-US', {
        hour12: true,
        timeZone: 'America/New_York',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
    });
}

function parseDuration(duration) {
    const parts = duration.split(', ');
    let totalMinutes = 0;
    
    parts.forEach(part => {
        if (part.includes('min')) {
            totalMinutes += parseInt(part);
        } else if (part.includes('sec')) {
            totalMinutes += parseInt(part) / 60;
        }
    });
    
    return totalMinutes;
}

function displayStats() {
    if (allJobs.length === 0) return;
    
    const totalJobs = allJobs.length;
    const uniqueTenants = new Set(allJobs.map(job => job.tenant)).size;
    const longestJob = allJobs.reduce((max, job) => 
        job.durationMinutes > max.durationMinutes ? job : max, allJobs[0]);
    
    // Time range calculation
    const firstJob = allJobs[0];
    const lastJob = allJobs[allJobs.length - 1];
    const timeRange = `${firstJob.time} - ${lastJob.time}`;
    
    // Peak hour calculation
    const hourCounts = {};
    allJobs.forEach(job => {
        const hour = job.time.substr(0, 2);
        hourCounts[hour] = (hourCounts[hour] || 0) + 1;
    });
    const peakHour = Object.entries(hourCounts).reduce((max, [hour, count]) => 
        count > max.count ? {hour, count} : max, {hour: '00', count: 0});
    
    // Safely update elements
    const elements = {
        'totalJobs': totalJobs,
        'uniqueTenants': uniqueTenants,
        'longestJob': `${longestJob.duration} (${longestJob.tenant})`,
        'timeRange': timeRange,
        'peakHour': `${peakHour.hour}:XX (${peakHour.count} jobs)`
    };
    
    for (const [id, value] of Object.entries(elements)) {
        const el = document.getElementById(id);
        if (el) el.textContent = value;
    }
}

function updateGridColumns() {
    const gridTemplate = Object.values(columnWidths).map(w => w + 'px').join(' ');

    const header = document.querySelector('.timeline-header');
    if (header) {
        header.style.gridTemplateColumns = gridTemplate;
    }
    document.querySelectorAll('.job-entry').forEach(entry => {
        entry.style.gridTemplateColumns = gridTemplate;
    });
}

function startResize(e, column) {
    e.stopPropagation();
    e.preventDefault();
    isResizing = true;
    currentResizeColumn = column;
    document.body.classList.add('resizing');

    const startX = e.clientX;
    const startWidth = columnWidths[column];

    function doResize(e) {
        if (!isResizing) return;
        const deltaX = e.clientX - startX;
        const newWidth = Math.max(80, startWidth + deltaX);

        columnWidths[column] = newWidth;
        updateGridColumns();
    }

    function stopResize() {
        isResizing = false;
        currentResizeColumn = null;
        document.body.classList.remove('resizing');
        document.removeEventListener('mousemove', doResize);
        document.removeEventListener('mouseup', stopResize);
    }

    document.addEventListener('mousemove', doResize);
    document.addEventListener('mouseup', stopResize);
}

function getJobClass(job) {
    if (job.durationMinutes > 30) return 'long-duration';
    if (job.durationMinutes > 5) return 'medium-duration';
    if (job.jobType === 'ClearingHouse') return 'clearing';
    if (job.jobType === 'ClinicalOperations') return 'clinical';
    if (job.jobType === 'QuicksightExport') return 'quicksight';
    return 'normal';
}

function applyFilters() {
    const tenantFilter = document.getElementById('tenantFilter').value.toLowerCase();
    const jobTypeFilter = document.getElementById('jobTypeFilter').value;
    const minDuration = parseFloat(document.getElementById('minDuration').value) || 0;
    const streamFilter = document.getElementById('streamFilter').value.toLowerCase();
    
    filteredJobs = allJobs.filter(job => {
        if (tenantFilter && !job.tenant.toLowerCase().includes(tenantFilter)) return false;
        if (jobTypeFilter && job.jobType !== jobTypeFilter) return false;
        if (job.durationMinutes < minDuration) return false;
        if (streamFilter && !job.logStream.toLowerCase().includes(streamFilter)) return false;
        return true;
    });
    
    displayTimeline();
}

function clearFilters() {
    document.getElementById('tenantFilter').value = '';
    document.getElementById('jobTypeFilter').value = '';
    document.getElementById('minDuration').value = '';
    document.getElementById('streamFilter').value = '';
    filteredJobs = [...allJobs];
    displayTimeline();
}

function displayChart() {
    const chart = document.getElementById('timelineChart');
    const tooltip = document.getElementById('chartTooltip');
    if (!chart || !tooltip) return;

    chart.innerHTML = '';

    // Group jobs by hour (EST)
    const hourCounts = {};
    allJobs.forEach(job => {
        const hour = parseInt(job.time.substr(0, 2));
        if (!hourCounts[hour]) {
            hourCounts[hour] = { count: 0, jobs: [] };
        }
        hourCounts[hour].count++;
        hourCounts[hour].jobs.push(job);
    });

    // Find max count for scaling (ensure minimum height for visibility)
    const maxCount = Math.max(...Object.values(hourCounts).map(h => h.count), 1);

    // Create bars for each hour (0-23)
    for (let hour = 0; hour < 24; hour++) {
        const count = hourCounts[hour] ? hourCounts[hour].count : 0;
        const jobs = hourCounts[hour] ? hourCounts[hour].jobs : [];
        // Ensure minimum height of 5px for visibility, max 180px
        const height = count === 0 ? 0 : Math.max(5, (count / maxCount) * 180);

        const bar = document.createElement('div');
        bar.className = 'chart-bar';
        if (count > maxCount * 0.7) bar.className += ' high-load';
        else if (count > maxCount * 0.4) bar.className += ' medium-load';

        bar.style.left = `${(hour / 24) * 100}%`;
        bar.style.width = `${(1 / 24) * 100 - 1}%`; // Slightly less width for spacing
        bar.style.height = `${height}px`;

        // Add hover tooltip
        bar.addEventListener('mouseenter', (e) => {
            tooltip.style.display = 'block';
            tooltip.style.left = e.pageX + 10 + 'px';
            tooltip.style.top = e.pageY - 30 + 'px';
            tooltip.innerHTML = `
                ${hour.toString().padStart(2, '0')}:00 EST - ${count} jobs<br>
                ${jobs.length > 0 ? 'Top tenants: ' + [...new Set(jobs.map(j => j.tenant))].slice(0, 3).join(', ') : 'No jobs'}
            `;
        });

        bar.addEventListener('mouseleave', () => {
            tooltip.style.display = 'none';
        });

        chart.appendChild(bar);

        // Add hour labels (show every 2 hours to avoid crowding)
        if (hour % 2 === 0) {
            const label = document.createElement('div');
            label.className = 'chart-axis';
            label.style.left = `${(hour / 24) * 100 + (1/48)*100}%`;
            label.textContent = hour.toString().padStart(2, '0') + ':00';
            chart.appendChild(label);
        }
    }
}

function sortBy(field) {
    if (filteredJobs.length === 0) return;

    // Update sort direction
    if (currentSort.field === field) {
        currentSort.direction = currentSort.direction === 'asc' ? 'desc' : 'asc';
    } else {
        currentSort.field = field;
        currentSort.direction = 'asc';
    }

    // Update header indicators
    document.querySelectorAll('.timeline-header div').forEach(header => {
        header.classList.remove('sorted', 'asc', 'desc');
    });

    // Map field names to column classes
    const fieldToColumn = {
        'time': 'col-time',
        'tenant': 'col-tenant',
        'jobType': 'col-jobtype',
        'duration': 'col-duration',
        'status': 'col-status',
        'operation': 'col-operation',
        'logStream': 'col-stream'
    };

    const header = document.querySelector(`.${fieldToColumn[field]}`);
    if (header) {
        header.classList.add('sorted', currentSort.direction);
    }

    // Sort the data
    filteredJobs.sort((a, b) => {
        let aVal = a[field];
        let bVal = b[field];

        // Special handling for different field types
        if (field === 'duration') {
            aVal = a.durationMinutes;
            bVal = b.durationMinutes;
        } else if (field === 'time') {
            // Use timestamp for proper chronological sorting
            aVal = a.timestamp.getTime();
            bVal = b.timestamp.getTime();
        }

        // Handle string comparisons
        if (typeof aVal === 'string') {
            aVal = aVal.toLowerCase();
            bVal = bVal.toLowerCase();
        }

        // Perform comparison
        let result;
        if (aVal < bVal) {
            result = -1;
        } else if (aVal > bVal) {
            result = 1;
        } else {
            result = 0;
        }

        // Apply sort direction
        return currentSort.direction === 'asc' ? result : -result;
    });

    displayTimeline();
}

function displayTimeline() {
    const timelineContent = document.getElementById('timelineContent');
    if (!timelineContent) return;

    timelineContent.innerHTML = '';

    // Ensure grid columns are properly set
    updateGridColumns();

    filteredJobs.forEach(job => {
        const entry = document.createElement('div');
        entry.className = `job-entry ${getJobClass(job)}`;

        // Extract short stream name (last part)
        const shortStream = job.logStream.split('/').pop().substr(0, 8) + '...';

        entry.innerHTML = `
            <div class="col-time">${job.time}</div>
            <div class="col-tenant">${job.tenant}</div>
            <div class="col-jobtype">${job.jobType}</div>
            <div class="col-duration">${job.duration}</div>
            <div class="col-status ${job.statusClass}">${job.status}</div>
            <div class="col-operation" title="${job.operation}">${job.operation.replace(/.*\./, '')}</div>
            <div class="col-stream" style="font-size: 10px; color: #999;" title="${job.logStream}">${shortStream}</div>
        `;

        timelineContent.appendChild(entry);
    });

    // Update results count
    if (document.getElementById('resultsCount')) {
        document.getElementById('resultsCount').textContent = `Showing ${filteredJobs.length} of ${allJobs.length} jobs`;
    } else {
        const resultsDiv = document.createElement('div');
        resultsDiv.id = 'resultsCount';
        resultsDiv.style.cssText = 'margin: 10px 0; font-weight: bold; color: #666;';
        resultsDiv.textContent = `Showing ${filteredJobs.length} of ${allJobs.length} jobs`;
        const timeline = document.getElementById('timeline');
        if (timeline && timeline.firstChild && timeline.firstChild.nextSibling) {
            timeline.insertBefore(resultsDiv, timeline.firstChild.nextSibling);
        }
    }
}

function exportFilteredData() {
    if (filteredJobs.length === 0) {
        alert('No data to export');
        return;
    }

    const csvHeader = 'Timestamp,Tenant,JobType,Duration,DurationMinutes,Operation,LogStream\n';
    const csvData = filteredJobs.map(job =>
        `${job.timestamp.toISOString()},${job.tenant},${job.jobType},"${job.duration}",${job.durationMinutes},"${job.operation}",${job.logStream}`
    ).join('\n');

    const blob = new Blob([csvHeader + csvData], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.style.display = 'none';
    a.href = url;
    a.download = 'cloudwatch_timeline.csv';
    document.body.appendChild(a);
    a.click();
    window.URL.revokeObjectURL(url);
    document.body.removeChild(a);
}
